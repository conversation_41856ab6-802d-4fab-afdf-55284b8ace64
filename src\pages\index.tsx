import { useNavigate } from 'react-router-dom';
import { LoginScreen } from '../components/auth/LoginScreen';
import { ErrorBoundary } from '../components/common';

export default function HomePage() {
  const navigate = useNavigate();

  const handleLogin = (credentials: any) => {
    console.log('Login successful:', credentials);
    // TODO: Implement actual login logic
    // For now, redirect to demo page
    navigate('/demo');
  };

  const handleAccessRequest = (request: any) => {
    console.log('Access request submitted:', request);
    // TODO: Implement access request logic
  };

  return (
    <ErrorBoundary
      level="section"
      componentName="LoginScreen"
      enableAutoRecovery={true}
      enableReporting={true}
    >
      <LoginScreen
        onLogin={handleLogin}
        onAccessRequest={handleAccessRequest}
        data-testid="login-page"
      />
    </ErrorBoundary>
  );
}
