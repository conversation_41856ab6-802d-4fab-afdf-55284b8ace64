import { useEffect } from 'react';
import { useTheme } from './hooks/useTheme';
import { useAppStore } from './stores/appStore';
import { ThemeToggle } from './components/ThemeToggle';
import { UserList } from './components/UserList';
import { AddUser } from './components/AddUser';
import { Button, Card, Input } from './components/ui';
import { initializeApp } from './utils/appInitializer';

function App() {
  useTheme(); // Initialize theme
  const { isInitialized, isLoading, configuration } = useAppStore();

  // Initialize app on start
  useEffect(() => {
    initializeApp();
  }, []);

  // Show loading state while app is initializing
  if (isLoading || !isInitialized) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-text-secondary">Initializing application...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background transition-colors">
      {/* Header */}
      <header className="bg-surface border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-text">
                {configuration.appName || 'Nexed Web'}
              </h1>
              <div className="hidden sm:flex items-center space-x-2 text-sm text-text-secondary">
                <span>React</span>
                <span>•</span>
                <span>TypeScript</span>
                <span>•</span>
                <span>Tailwind v4</span>
                <span>•</span>
                <span>Zustand</span>
                <span>•</span>
                <span>MSW</span>
                <span>•</span>
                <span>Vitest</span>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="text-center">
            <h2 className="text-3xl font-bold text-text mb-4">
              Welcome to Your Modern React Setup
            </h2>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              This application demonstrates a complete React TypeScript setup
              with Vite, Tailwind CSS v4, Zustand state management, MSW for API
              mocking, and Vitest for testing.
            </p>
          </div>

          {/* Component Demo Section */}
          <div className="grid md:grid-cols-2 gap-8">
            <Card variant="elevated" padding="lg">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">
                Component Library Demo
              </h3>
              <div className="space-y-4">
                <div>
                  <Input
                    label="Sample Input"
                    placeholder="Try typing here..."
                    helperText="This input uses the theme colors"
                  />
                </div>
                <div className="flex space-x-2">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                </div>
                <Card variant="outlined" padding="sm">
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Nested card component with theme integration
                  </p>
                </Card>
              </div>
            </Card>

            <Card variant="elevated" padding="lg">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">
                User Management Demo
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                Zustand state management with MSW API mocking.
              </p>
              <AddUser />
              <UserList />
            </Card>
          </div>

          {/* Features Section */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                title: '⚡ Vite',
                description:
                  'Lightning fast development with HMR and optimized builds',
              },
              {
                title: '🎨 Tailwind CSS v4',
                description:
                  'Modern utility-first CSS framework with dark mode support',
              },
              {
                title: '🐻 Zustand',
                description:
                  'Lightweight state management with TypeScript support',
              },
              {
                title: '🎭 MSW',
                description:
                  'API mocking for development and testing environments',
              },
              {
                title: '🧪 Vitest',
                description:
                  'Fast unit testing with React Testing Library integration',
              },
              {
                title: '🪝 Husky',
                description:
                  'Git hooks for code quality with ESLint and Prettier',
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow"
              >
                <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                  {feature.title}
                </h4>
                <p className="text-slate-600 dark:text-slate-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;
