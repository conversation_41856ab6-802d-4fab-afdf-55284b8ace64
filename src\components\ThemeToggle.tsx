import React, { useState } from 'react';
import { useTheme } from '../hooks/useTheme';
import type { Theme } from '../stores/themeStore';

// Custom SVG icons for better control and animation
const SunIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg
    className={`transition-all duration-300 ${className}`}
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="4" stroke="currentColor" strokeWidth="2" />
    <path
      d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41"
      stroke="currentColor"
      strokeWidth="2"
    />
  </svg>
);

const MoonIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg
    className={`transition-all duration-300 ${className}`}
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
      stroke="currentColor"
      strokeWidth="2"
      fill="currentColor"
    />
  </svg>
);

const SystemIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg
    className={`transition-all duration-300 ${className}`}
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="2"
      y="3"
      width="20"
      height="14"
      rx="2"
      ry="2"
      stroke="currentColor"
      strokeWidth="2"
    />
    <line
      x1="8"
      y1="21"
      x2="16"
      y2="21"
      stroke="currentColor"
      strokeWidth="2"
    />
    <line
      x1="12"
      y1="17"
      x2="12"
      y2="21"
      stroke="currentColor"
      strokeWidth="2"
    />
  </svg>
);

export interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  'data-testid'?: string;
}

export function ThemeToggle({
  className = '',
  showLabel = false,
  size = 'md',
  'data-testid': testId,
}: ThemeToggleProps) {
  const { theme, setTheme, colors } = useTheme();
  const [isAnimating, setIsAnimating] = useState(false);

  const themes: Array<{
    value: Theme;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
  }> = [
    { value: 'light', label: 'Light Mode', icon: SunIcon },
    { value: 'dark', label: 'Dark Mode', icon: MoonIcon },
    { value: 'system', label: 'System', icon: SystemIcon },
  ];

  const currentThemeIndex = themes.findIndex(t => t.value === theme);
  const currentTheme = themes[currentThemeIndex];

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const handleToggle = async () => {
    if (isAnimating) return;

    setIsAnimating(true);

    // Cycle through themes: light -> dark -> system -> light
    const nextIndex = (currentThemeIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];

    // Add a small delay for animation effect
    setTimeout(() => {
      setTheme(nextTheme.value);
      setIsAnimating(false);
    }, 150);
  };

  const IconComponent = currentTheme.icon;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showLabel && (
        <span className="text-sm font-medium" style={{ color: colors.text }}>
          {currentTheme.label}
        </span>
      )}

      <button
        onClick={handleToggle}
        className={`
          ${sizeClasses[size]}
          relative
          rounded-full
          border-0
          bg-transparent
          focus:outline-none
          focus:ring-2
          focus:ring-offset-2
          transition-all
          duration-300
          ease-in-out
          group
          disabled:opacity-50
          disabled:cursor-not-allowed
        `}
        style={
          {
            '--focus-ring-color': colors.primary,
          } as React.CSSProperties & { '--focus-ring-color': string }
        }
        onMouseEnter={e => {
          if (!isAnimating) {
            e.currentTarget.style.backgroundColor = colors.hover;
          }
        }}
        onMouseLeave={e => {
          if (!isAnimating) {
            e.currentTarget.style.backgroundColor = 'transparent';
          }
        }}
        disabled={isAnimating}
        aria-label={`Switch to next theme (current: ${currentTheme.label})`}
        title={`Current: ${currentTheme.label}. Click to cycle themes.`}
        data-testid={testId}
      >
        <div
          className={`
            ${iconSizeClasses[size]}
            absolute
            inset-0
            m-auto
            transform
            transition-all
            duration-300
            ease-in-out
            ${isAnimating ? 'scale-75 rotate-180 opacity-0' : 'scale-100 rotate-0 opacity-100'}
          `}
          style={{ color: colors.text }}
        >
          <IconComponent className="w-full h-full" />
        </div>

        {/* Ripple effect on click */}
        <div
          className={`
            absolute
            inset-0
            rounded-full
            transition-all
            duration-300
            ease-out
            ${isAnimating ? 'scale-150 opacity-20' : 'scale-0 opacity-0'}
          `}
          style={{ backgroundColor: colors.primary }}
        />
      </button>
    </div>
  );
}
