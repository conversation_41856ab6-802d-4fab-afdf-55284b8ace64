
import { useThemeStore } from '../../stores/themeStore';

export interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  className?: string;
  showText?: boolean;
  textClassName?: string;
  'data-testid'?: string;
}

const sizeClasses = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-12 w-12',
  xl: 'h-16 w-16',
};

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl',
  xl: 'text-3xl',
};

export function Logo({
  size = 'md',
  variant = 'full',
  className = '',
  showText = true,
  textClassName = '',
  'data-testid': testId,
}: LogoProps) {
  const { colors } = useThemeStore();

  const LogoIcon = () => (
    <svg
      className={`${sizeClasses[size]} ${className}`}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      data-testid={testId ? `${testId}-icon` : 'logo-icon'}
    >
      {/* Outer ring */}
      <circle
        cx="24"
        cy="24"
        r="22"
        stroke="url(#gradient1)"
        strokeWidth="2"
        fill="none"
      />

      {/* Inner geometric pattern */}
      <path
        d="M16 16L32 32M32 16L16 32"
        stroke="url(#gradient2)"
        strokeWidth="2"
        strokeLinecap="round"
      />

      {/* Center diamond */}
      <path
        d="M24 12L32 24L24 36L16 24Z"
        fill="url(#gradient3)"
        stroke="url(#gradient1)"
        strokeWidth="1"
      />

      {/* Gradient definitions */}
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={colors.primary} />
          <stop offset="50%" stopColor={colors.secondary} />
          <stop offset="100%" stopColor={colors.accent} />
        </linearGradient>
        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={colors.secondary} />
          <stop offset="100%" stopColor={colors.accent} />
        </linearGradient>
        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor={`${colors.primary}20`} />
          <stop offset="100%" stopColor={`${colors.secondary}20`} />
        </linearGradient>
      </defs>
    </svg>
  );

  const LogoText = () => (
    <span
      className={`font-bold text-slate-900 dark:text-slate-100 ${textSizeClasses[size]} ${textClassName}`}
      data-testid={testId ? `${testId}-text` : 'logo-text'}
    >
      Nexed
    </span>
  );

  if (variant === 'icon') {
    return <LogoIcon />;
  }

  if (variant === 'text') {
    return <LogoText />;
  }

  // Full variant
  return (
    <div
      className={`flex items-center gap-3 ${className}`}
      data-testid={testId}
    >
      <LogoIcon />
      {showText && <LogoText />}
    </div>
  );
}

export default Logo;
