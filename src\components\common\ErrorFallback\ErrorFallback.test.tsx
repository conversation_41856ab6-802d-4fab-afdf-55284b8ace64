/**
 * ErrorFallback Component Tests
 * Comprehensive test suite for the ErrorFallback component
 */


import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ErrorFallback from './ErrorFallback';
import {
  createAppError,
  ErrorType,
  ErrorSeverity,
} from '../../../utils/errorTypes';
import * as errorRecovery from '../../../utils/errorRecovery';
import * as errorReporting from '../../../utils/errorReporting';

// Mock the utility modules
vi.mock('../../../utils/errorRecovery');
vi.mock('../../../utils/errorReporting');
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      error: '#ef4444',
      warning: '#f59e0b',
      text: '#1f2937',
      textSecondary: '#6b7280',
      primaryText: '#ffffff',
      secondaryText: '#ffffff',
      border: '#d1d5db',
    },
    theme: 'light',
  }),
}));

const mockGetRecoveryStrategies = vi.mocked(
  errorRecovery.getRecoveryStrategies
);
const mockExecuteRecoveryStrategy = vi.mocked(
  errorRecovery.executeRecoveryStrategy
);
const mockReportError = vi.mocked(errorReporting.reportError);

// Helper function to create test errors
const createTestError = (
  type: ErrorType = ErrorType.RUNTIME,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  message: string = 'Test error'
) => {
  return createAppError(new Error(message), {
    type,
    severity,
    userMessage: 'User-friendly error message',
    recoverable: true,
    retryable: true,
    reportable: true,
    suggestedActions: [],
  });
};

// Mock recovery strategies
const mockRecoveryStrategies = [
  {
    action: 'RETRY' as const,
    label: 'Try Again',
    description: 'Retry the failed operation',
    icon: '🔄',
    primary: true,
    handler: vi.fn(),
  },
  {
    action: 'RELOAD' as const,
    label: 'Reload Page',
    description: 'Refresh the current page',
    icon: '🔃',
    confirmationRequired: true,
    handler: vi.fn(),
  },
];

describe('ErrorFallback', () => {
  const mockResetError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetRecoveryStrategies.mockReturnValue(mockRecoveryStrategies);
    mockExecuteRecoveryStrategy.mockResolvedValue(undefined);
    mockReportError.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders error message correctly', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          componentName="TestComponent"
        />
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
      expect(
        screen.getByText('User-friendly error message')
      ).toBeInTheDocument();
      expect(screen.getByText('Component: TestComponent')).toBeInTheDocument();
    });

    it('renders different titles based on level', () => {
      const error = createTestError();

      const { rerender } = render(
        <ErrorFallback error={error} resetError={mockResetError} level="page" />
      );
      expect(screen.getByText('Application Error')).toBeInTheDocument();

      rerender(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          level="section"
        />
      );
      expect(screen.getByText('Section Error')).toBeInTheDocument();

      rerender(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          level="component"
        />
      );
      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });

    it('displays correct error icon based on severity', () => {
      const lowError = createTestError(ErrorType.VALIDATION, ErrorSeverity.LOW);
      const { rerender } = render(
        <ErrorFallback error={lowError} resetError={mockResetError} />
      );
      expect(screen.getByText('⚠️')).toBeInTheDocument();

      const criticalError = createTestError(
        ErrorType.RUNTIME,
        ErrorSeverity.CRITICAL
      );
      rerender(
        <ErrorFallback error={criticalError} resetError={mockResetError} />
      );
      expect(screen.getByText('🚨')).toBeInTheDocument();
    });
  });

  describe('Recovery Actions', () => {
    it('renders recovery strategy buttons', () => {
      const error = createTestError();

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      expect(
        screen.getByText('What would you like to do?')
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /try again/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /reload page/i })
      ).toBeInTheDocument();
    });

    it('executes recovery strategy when button is clicked', async () => {
      const error = createTestError();

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(mockExecuteRecoveryStrategy).toHaveBeenCalledWith(
          mockRecoveryStrategies[0],
          expect.objectContaining({
            error,
            userInitiated: true,
          })
        );
      });
    });

    it('shows confirmation dialog for actions that require confirmation', async () => {
      const error = createTestError();
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      const reloadButton = screen.getByRole('button', { name: /reload page/i });
      fireEvent.click(reloadButton);

      expect(confirmSpy).toHaveBeenCalledWith(
        expect.stringContaining('Are you sure you want to reload page?')
      );

      confirmSpy.mockRestore();
    });

    it('does not execute recovery strategy if confirmation is cancelled', async () => {
      const error = createTestError();
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false);

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      const reloadButton = screen.getByRole('button', { name: /reload page/i });
      fireEvent.click(reloadButton);

      expect(mockExecuteRecoveryStrategy).not.toHaveBeenCalled();

      confirmSpy.mockRestore();
    });

    it('calls resetError after successful recovery', async () => {
      const error = createTestError();

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(mockResetError).toHaveBeenCalled();
      });
    });
  });

  describe('Technical Details', () => {
    it('toggles technical details visibility', () => {
      const error = createTestError();

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      // Details should be hidden initially
      expect(screen.queryByText('Type:')).not.toBeInTheDocument();

      // Click to show details
      const toggleButton = screen.getByText('Show Technical Details');
      fireEvent.click(toggleButton);

      expect(screen.getByText('Type:')).toBeInTheDocument();
      expect(screen.getByText('RUNTIME')).toBeInTheDocument();
      expect(screen.getByText('Hide Technical Details')).toBeInTheDocument();

      // Click to hide details
      fireEvent.click(screen.getByText('Hide Technical Details'));
      expect(screen.queryByText('Type:')).not.toBeInTheDocument();
    });

    it('shows technical details by default when showDetails prop is true', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          showDetails={true}
        />
      );

      expect(screen.getByText('Type:')).toBeInTheDocument();
      expect(screen.getByText('RUNTIME')).toBeInTheDocument();
    });

    it('displays error stack trace when available', () => {
      const errorWithStack = createTestError();
      errorWithStack.stack =
        'Error: Test error\n    at TestComponent\n    at App';

      render(
        <ErrorFallback
          error={errorWithStack}
          resetError={mockResetError}
          showDetails={true}
        />
      );

      expect(screen.getByText('Stack Trace')).toBeInTheDocument();
    });
  });

  describe('User Feedback', () => {
    it('renders user feedback section when enabled', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          enableUserFeedback={true}
        />
      );

      expect(
        screen.getByText('Help us improve (optional)')
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText(/what were you trying to do/i)
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /send feedback/i })
      ).toBeInTheDocument();
    });

    it('does not render user feedback section when disabled', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          enableUserFeedback={false}
        />
      );

      expect(
        screen.queryByText('Help us improve (optional)')
      ).not.toBeInTheDocument();
    });

    it('submits user feedback', async () => {
      const error = createTestError();
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          enableUserFeedback={true}
        />
      );

      const textarea = screen.getByPlaceholderText(
        /what were you trying to do/i
      );
      const submitButton = screen.getByRole('button', {
        name: /send feedback/i,
      });

      // Type feedback
      fireEvent.change(textarea, {
        target: { value: 'I was trying to load the page' },
      });

      // Submit feedback
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockReportError).toHaveBeenCalledWith(
          error,
          expect.objectContaining({
            userFeedback: 'I was trying to load the page',
          })
        );
      });

      expect(alertSpy).toHaveBeenCalledWith(
        expect.stringContaining('Thank you for your feedback')
      );

      alertSpy.mockRestore();
    });

    it('shows character count for feedback', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          enableUserFeedback={true}
        />
      );

      const textarea = screen.getByPlaceholderText(
        /what were you trying to do/i
      );

      expect(screen.getByText('0/500 characters')).toBeInTheDocument();

      fireEvent.change(textarea, { target: { value: 'Test feedback' } });
      expect(screen.getByText('13/500 characters')).toBeInTheDocument();
    });

    it('disables submit button when feedback is empty', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          enableUserFeedback={true}
        />
      );

      const submitButton = screen.getByRole('button', {
        name: /send feedback/i,
      });
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    it('has proper data-testid when provided', () => {
      const error = createTestError();

      render(
        <ErrorFallback
          error={error}
          resetError={mockResetError}
          data-testid="error-fallback"
        />
      );

      expect(screen.getByTestId('error-fallback')).toBeInTheDocument();
    });

    it('has proper button roles and labels', () => {
      const error = createTestError();

      render(<ErrorFallback error={error} resetError={mockResetError} />);

      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);

      buttons.forEach(button => {
        expect(button).toHaveAttribute('type', 'button');
      });
    });
  });
});
